#!/usr/bin/env python3
"""
Simple test script for your working Document Parser API
Use this to quickly test file parsing functionality
"""

import requests
import json

# Your working API URL
API_URL = "https://parser-gray.vercel.app"

def test_text_parsing():
    """Test parsing a simple text file"""
    print("🧪 Testing text file parsing...")
    
    # Create test content
    test_content = """Sample Document for Testing

This is a test document with multiple paragraphs.

Key Information:
- Name: <PERSON>: <EMAIL>
- Phone: (*************

Additional Notes:
The document parser should extract all this text content
and return it in a structured format.

End of document."""
    
    # Save to temporary file
    with open("sample.txt", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    try:
        # Upload and parse
        with open("sample.txt", "rb") as f:
            files = {"file": f}
            data = {"return_metadata": "true"}
            
            response = requests.post(f"{API_URL}/parse", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Text parsing successful!")
            print(f"📄 File: {result['filename']}")
            print(f"📊 Text length: {result['total_text_length']} characters")
            print(f"📝 Content preview:")
            
            for i, item in enumerate(result['text_content'][:3]):
                print(f"   Line {item.get('line', i+1)}: {item['content'][:60]}...")
            
            return result
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None
    finally:
        # Clean up
        import os
        if os.path.exists("sample.txt"):
            os.remove("sample.txt")

def test_api_status():
    """Test API health and status"""
    print("🔍 Checking API status...")
    
    try:
        response = requests.get(f"{API_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print("✅ API is healthy!")
            print(f"📊 Dependencies:")
            for dep, status in data['dependencies'].items():
                emoji = "✅" if status else "❌"
                print(f"   {emoji} {dep.upper()}: {'Available' if status else 'Not available'}")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot reach API: {str(e)}")
        return False

def parse_your_file(file_path):
    """Parse your own file"""
    print(f"📁 Parsing your file: {file_path}")
    
    try:
        with open(file_path, "rb") as f:
            files = {"file": f}
            data = {"return_metadata": "true"}
            
            response = requests.post(f"{API_URL}/parse", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ File parsing successful!")
            print(f"📄 File: {result['filename']}")
            print(f"📊 Type: {result['file_type']}")
            print(f"📊 Text length: {result['total_text_length']} characters")
            
            # Show first few lines
            text_content = result.get('text_content', [])
            if text_content:
                print(f"📝 Content preview:")
                for i, item in enumerate(text_content[:5]):
                    content = item.get('content', '')[:80]
                    line_num = item.get('line', item.get('page', i+1))
                    print(f"   {line_num}: {content}...")
            
            # Save full result
            output_file = f"parsed_{file_path.replace('/', '_').replace('\\', '_')}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"💾 Full results saved to: {output_file}")
            
            return result
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Error: {response.text}")
            return None
            
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def main():
    """Main function"""
    print("🚀 Document Parser API - Simple Test")
    print("=" * 50)
    print(f"🌐 API URL: {API_URL}")
    print()
    
    # Test API status
    if not test_api_status():
        print("❌ API is not responding. Check your deployment.")
        return
    
    print()
    
    # Test with sample file
    test_text_parsing()
    
    print()
    print("=" * 50)
    print("💡 To test your own file, use:")
    print("   python simple_test.py your_file.pdf")
    print("   python simple_test.py your_document.docx")
    print("   python simple_test.py your_spreadsheet.xlsx")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Parse user-provided file
        file_path = sys.argv[1]
        print("🚀 Document Parser API - File Test")
        print("=" * 50)
        
        if test_api_status():
            print()
            parse_your_file(file_path)
    else:
        # Run standard tests
        main()
