[{"url": "https://parser-gray.vercel.app", "timestamp": "2025-08-07 22:23:42", "tests": {"root": {"success": true, "data": {"message": "Document Parser API is running", "version": "1.0.0", "supported_formats": ["PDF", "DOCX", "PPTX", "XLSX", "TXT"], "features": {"ocr_available": false, "docx_available": true, "pptx_available": true, "excel_available": true}}}, "health": {"success": true, "data": {"status": "healthy", "timestamp": "2025-08-07T16:53:44.286007", "dependencies": {"ocr": false, "docx": true, "pptx": true, "excel": true}}}, "supported_formats": {"success": true, "data": {"formats": {"PDF": {"extensions": [".pdf"], "description": "Portable Document Format", "ocr_supported": false}, "DOCX": {"extensions": [".docx"], "description": "Microsoft Word Document", "available": true}, "PPTX": {"extensions": [".pptx"], "description": "Microsoft PowerPoint Presentation", "available": true}, "XLSX": {"extensions": [".xlsx"], "description": "Microsoft Excel Spreadsheet", "available": true}, "TXT": {"extensions": [".txt"], "description": "Plain Text File", "available": true}}}}, "parse": {"success": true, "data": {"success": true, "filename": "test_document.txt", "file_type": "txt", "processed_at": "2025-08-07T16:53:45.933444", "text_content": [{"line": 1, "content": "This is a test document for the Document Parser API."}, {"line": 3, "content": "It contains multiple lines of text to test the parsing functionality."}, {"line": 5, "content": "Line 1: Hello World"}, {"line": 6, "content": "Line 2: This is a sample document"}, {"line": 7, "content": "Line 3: Testing the API functionality"}, {"line": 9, "content": "End of test document."}], "total_text_length": 239, "metadata": {"total_lines": 9, "non_empty_lines": 6, "encoding": "ascii", "confidence": 1.0, "file_size": 247, "file_path": "/tmp/tmpf9sp4f73.txt", "file_name": "tmpf9sp4f73.txt", "file_type": "txt", "processed_at": "2025-08-07T16:53:45.932312"}}}, "parse_url": {"success": false, "status_code": 400, "error": "{\"detail\":\"Failed to download file: 404 Client Error: Not Found for url: https://www.w3.org/TR/png/iso_8859-1.txt\"}"}}, "success_rate": 80.0, "successful_tests": 4, "total_tests": 5}]