#!/usr/bin/env python3
"""
Simple installation script for document parser dependencies
Run this before using document_parser.py
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Install all required dependencies"""
    print("Installing Document Parser Dependencies...")
    print("=" * 50)
    
    packages = [
        "PyPDF2",
        "pdfplumber",
        "pymupdf",
        "python-docx",
        "python-pptx",
        "openpyxl",
        "xlrd",
        "pytesseract",
        "Pillow",
        "pdf2image",
        "requests",
        "python-dotenv",
        "tqdm",
        "chardet"
    ]
    
    failed = []
    
    for package in packages:
        print(f"Installing {package}...", end=" ")
        if install_package(package):
            print("✓")
        else:
            print("✗")
            failed.append(package)
    
    print("\n" + "=" * 50)
    
    if failed:
        print(f"Failed to install: {', '.join(failed)}")
        print("Try installing manually with:")
        print(f"pip install {' '.join(failed)}")
    else:
        print("✓ All dependencies installed successfully!")
        print("\nNext steps:")
        print("1. Configure your API URL in the .env file")
        print("2. Test installation: python document_parser.py --check-install")
        print("3. Parse a document: python document_parser.py your_file.pdf")

if __name__ == "__main__":
    main()
