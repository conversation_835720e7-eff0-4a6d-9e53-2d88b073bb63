#!/usr/bin/env python3
"""
Test script for Document Parser API deployed on Vercel
Tests all endpoints and functionality
"""

import requests
import json
import time
import os
from typing import Dict, List

# Your Vercel URLs
URLS = [
    "https://parser-gray.vercel.app"
]

def test_health_endpoint(base_url: str) -> Dict:
    """Test the health endpoint"""
    print(f"\n🔍 Testing health endpoint: {base_url}/health")
    
    try:
        response = requests.get(f"{base_url}/health", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed")
            print(f"   Status: {data.get('status', 'unknown')}")
            print(f"   Dependencies: {data.get('dependencies', {})}")
            return {"success": True, "data": data}
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return {"success": False, "status_code": response.status_code, "error": response.text}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {str(e)}")
        return {"success": False, "error": str(e)}

def test_root_endpoint(base_url: str) -> Dict:
    """Test the root endpoint"""
    print(f"\n🔍 Testing root endpoint: {base_url}/")
    
    try:
        response = requests.get(f"{base_url}/", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Root endpoint passed")
            print(f"   Message: {data.get('message', 'unknown')}")
            print(f"   Supported formats: {data.get('supported_formats', [])}")
            return {"success": True, "data": data}
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return {"success": False, "status_code": response.status_code, "error": response.text}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Root endpoint failed: {str(e)}")
        return {"success": False, "error": str(e)}

def test_supported_formats_endpoint(base_url: str) -> Dict:
    """Test the supported formats endpoint"""
    print(f"\n🔍 Testing supported formats: {base_url}/supported-formats")
    
    try:
        response = requests.get(f"{base_url}/supported-formats", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Supported formats endpoint passed")
            formats = data.get('formats', {})
            for format_name, details in formats.items():
                available = details.get('available', details.get('ocr_supported', True))
                status = "✅" if available else "❌"
                print(f"   {status} {format_name}: {details.get('description', 'No description')}")
            return {"success": True, "data": data}
        else:
            print(f"❌ Supported formats failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return {"success": False, "status_code": response.status_code, "error": response.text}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Supported formats failed: {str(e)}")
        return {"success": False, "error": str(e)}

def create_test_file() -> str:
    """Create a test text file for parsing"""
    test_content = """This is a test document for the Document Parser API.

It contains multiple lines of text to test the parsing functionality.

Line 1: Hello World
Line 2: This is a sample document
Line 3: Testing the API functionality

End of test document."""
    
    test_file_path = "test_document.txt"
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    return test_file_path

def test_parse_endpoint(base_url: str) -> Dict:
    """Test the parse endpoint with a file upload"""
    print(f"\n🔍 Testing parse endpoint: {base_url}/parse")
    
    # Create test file
    test_file = create_test_file()
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': (test_file, f, 'text/plain')}
            data = {
                'use_ocr': 'false',
                'return_metadata': 'true'
            }
            
            response = requests.post(f"{base_url}/parse", files=files, data=data, timeout=60)
        
        # Clean up test file
        if os.path.exists(test_file):
            os.remove(test_file)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Parse endpoint passed")
            print(f"   Filename: {result.get('filename', 'unknown')}")
            print(f"   File type: {result.get('file_type', 'unknown')}")
            print(f"   Text length: {result.get('total_text_length', 0)}")
            
            text_content = result.get('text_content', [])
            if text_content:
                print(f"   First line: {text_content[0].get('content', '')[:50]}...")
            
            return {"success": True, "data": result}
        else:
            print(f"❌ Parse endpoint failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return {"success": False, "status_code": response.status_code, "error": response.text}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Parse endpoint failed: {str(e)}")
        # Clean up test file
        if os.path.exists(test_file):
            os.remove(test_file)
        return {"success": False, "error": str(e)}

def test_parse_url_endpoint(base_url: str) -> Dict:
    """Test the parse-url endpoint"""
    print(f"\n🔍 Testing parse-url endpoint: {base_url}/parse-url")
    
    # Use a simple text file URL for testing
    test_url = "https://www.w3.org/TR/PNG/iso_8859-1.txt"
    
    try:
        data = {
            'url': test_url,
            'use_ocr': 'false',
            'return_metadata': 'true'
        }
        
        response = requests.post(f"{base_url}/parse-url", data=data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Parse-URL endpoint passed")
            print(f"   Source URL: {result.get('source_url', 'unknown')}")
            print(f"   File type: {result.get('file_type', 'unknown')}")
            print(f"   Text length: {result.get('total_text_length', 0)}")
            return {"success": True, "data": result}
        else:
            print(f"❌ Parse-URL endpoint failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return {"success": False, "status_code": response.status_code, "error": response.text}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Parse-URL endpoint failed: {str(e)}")
        return {"success": False, "error": str(e)}

def test_single_url(base_url: str) -> Dict:
    """Test all endpoints for a single URL"""
    print(f"\n{'='*60}")
    print(f"🚀 TESTING: {base_url}")
    print(f"{'='*60}")
    
    results = {
        "url": base_url,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "tests": {}
    }
    
    # Test all endpoints
    results["tests"]["root"] = test_root_endpoint(base_url)
    results["tests"]["health"] = test_health_endpoint(base_url)
    results["tests"]["supported_formats"] = test_supported_formats_endpoint(base_url)
    results["tests"]["parse"] = test_parse_endpoint(base_url)
    results["tests"]["parse_url"] = test_parse_url_endpoint(base_url)
    
    # Calculate success rate
    total_tests = len(results["tests"])
    successful_tests = sum(1 for test in results["tests"].values() if test.get("success", False))
    success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    results["success_rate"] = success_rate
    results["successful_tests"] = successful_tests
    results["total_tests"] = total_tests
    
    print(f"\n📊 SUMMARY for {base_url}")
    print(f"   Success Rate: {success_rate:.1f}% ({successful_tests}/{total_tests})")
    
    if success_rate == 100:
        print(f"   🎉 All tests passed! This URL is working perfectly.")
    elif success_rate >= 60:
        print(f"   ⚠️  Most tests passed. Some issues detected.")
    else:
        print(f"   ❌ Major issues detected. URL may not be working properly.")
    
    return results

def main():
    """Main test function"""
    print("🧪 Document Parser API Test Suite")
    print("=" * 60)
    
    all_results = []
    
    for url in URLS:
        try:
            result = test_single_url(url)
            all_results.append(result)
            time.sleep(2)  # Brief pause between URL tests
        except Exception as e:
            print(f"❌ Failed to test {url}: {str(e)}")
            all_results.append({
                "url": url,
                "error": str(e),
                "success_rate": 0
            })
    
    # Final summary
    print(f"\n{'='*60}")
    print("🏁 FINAL RESULTS")
    print(f"{'='*60}")
    
    working_urls = []
    for result in all_results:
        url = result["url"]
        success_rate = result.get("success_rate", 0)
        
        if success_rate >= 80:
            status = "🟢 WORKING"
            working_urls.append(url)
        elif success_rate >= 40:
            status = "🟡 PARTIAL"
        else:
            status = "🔴 FAILED"
        
        print(f"{status} {url} ({success_rate:.1f}%)")
    
    if working_urls:
        print(f"\n✅ RECOMMENDED URL(S) TO USE:")
        for url in working_urls:
            print(f"   🎯 {url}")
    else:
        print(f"\n❌ No fully working URLs found. Check deployment logs.")
    
    # Save detailed results
    with open("api_test_results.json", "w") as f:
        json.dump(all_results, f, indent=2)
    print(f"\n📄 Detailed results saved to: api_test_results.json")

if __name__ == "__main__":
    main()
