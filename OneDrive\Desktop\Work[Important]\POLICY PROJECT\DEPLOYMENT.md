# Document Parser API - Vercel Deployment Guide

## Overview

This guide explains how to deploy the Document Parser API to Vercel. The API accepts file uploads and returns extracted text content.

## Files Created for Vercel Deployment

1. **`api.py`** - FastAPI web application
2. **`vercel.json`** - Vercel configuration
3. **`requirements.txt`** - Python dependencies (updated for web deployment)
4. **`index.html`** - Simple web interface for testing

## API Endpoints

### `GET /`
Health check endpoint that returns API status and supported features.

### `POST /parse`
Main endpoint for parsing uploaded documents.

**Parameters:**
- `file` (required): Document file to parse
- `use_ocr` (optional): Enable OCR for image-based PDFs
- `return_metadata` (optional): Include metadata in response

**Example Response:**
```json
{
  "success": true,
  "filename": "document.pdf",
  "file_type": "pdf",
  "processed_at": "2025-01-07T12:00:00",
  "text_content": [
    {
      "page": 1,
      "content": "Extracted text content..."
    }
  ],
  "total_text_length": 1234,
  "metadata": {
    "total_pages": 5,
    "file_size": 102400
  }
}
```

### `POST /parse-url`
Parse a document from a URL.

**Parameters:**
- `url` (required): URL of the document to parse
- `use_ocr` (optional): Enable OCR for image-based PDFs
- `return_metadata` (optional): Include metadata in response

### `GET /health`
Detailed health check with dependency status.

### `GET /supported-formats`
Returns list of supported file formats and their availability.

## Deployment Steps

### 1. Prepare Your Files

Ensure you have these files in your project directory:
- `api.py`
- `document_parser.py`
- `vercel.json`
- `requirements.txt`
- `index.html` (optional, for testing)

### 2. Deploy to Vercel

#### Option A: Vercel CLI
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

#### Option B: GitHub Integration
1. Push your code to a GitHub repository
2. Connect your GitHub account to Vercel
3. Import your repository in Vercel dashboard
4. Deploy automatically

### 3. Test Your Deployment

Once deployed, you can test the API:

1. Visit your Vercel URL to see the health check
2. Use the `/parse` endpoint to upload files
3. Use the web interface at your domain for easy testing

## Usage Examples

### Using curl
```bash
# Parse a local file
curl -X POST "https://your-app.vercel.app/parse" \
  -F "file=@document.pdf" \
  -F "use_ocr=false" \
  -F "return_metadata=true"

# Parse from URL
curl -X POST "https://your-app.vercel.app/parse-url" \
  -F "url=https://example.com/document.pdf" \
  -F "use_ocr=false"
```

### Using JavaScript
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('use_ocr', 'false');
formData.append('return_metadata', 'true');

const response = await fetch('https://your-app.vercel.app/parse', {
  method: 'POST',
  body: formData
});

const result = await response.json();
console.log(result);
```

### Using Python
```python
import requests

# Parse a file
with open('document.pdf', 'rb') as f:
    files = {'file': f}
    data = {'use_ocr': 'false', 'return_metadata': 'true'}
    response = requests.post('https://your-app.vercel.app/parse', 
                           files=files, data=data)
    result = response.json()
    print(result)
```

## Limitations on Vercel

1. **File Size**: Maximum 10MB per file (Vercel limit)
2. **Execution Time**: 30 seconds maximum (configured in vercel.json)
3. **Memory**: Limited memory available
4. **OCR**: May not work due to system dependencies (Tesseract)
5. **Temporary Files**: Only `/tmp` directory is writable

## Supported File Formats

- **PDF**: ✅ Supported (via pdfplumber and PyMuPDF)
- **DOCX**: ✅ Supported (via python-docx)
- **PPTX**: ✅ Supported (via python-pptx)
- **XLSX**: ✅ Supported (via openpyxl)
- **TXT**: ✅ Supported (native)
- **OCR**: ⚠️ May not work on Vercel (system dependencies)

## Troubleshooting

### Common Issues

1. **Import Errors**: Check that all dependencies are in requirements.txt
2. **File Size Errors**: Ensure files are under 10MB
3. **Timeout Errors**: Large files may exceed 30-second limit
4. **OCR Not Working**: OCR requires system libraries not available on Vercel

### Debugging

Check Vercel function logs in your dashboard for detailed error messages.

## Environment Variables

You can set these in Vercel dashboard:
- `LOG_LEVEL`: Set logging level (DEBUG, INFO, WARNING, ERROR)
- Any other configuration variables from your original script

## Security Considerations

1. **File Validation**: API validates file types and sizes
2. **CORS**: Currently allows all origins - configure for production
3. **Rate Limiting**: Consider adding rate limiting for production use
4. **Authentication**: Add API keys if needed for production

## Next Steps

1. Test the deployment with various file types
2. Configure CORS for your specific domain
3. Add authentication if needed
4. Monitor usage and performance
5. Consider upgrading to Vercel Pro for higher limits if needed
