<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Parser Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }

        .upload-section {
            padding: 40px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }

        .results-section {
            padding: 40px;
            background: white;
        }

        .upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            padding: 60px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 30px;
        }

        .upload-area:hover, .upload-area.dragover {
            border-color: #4facfe;
            background: rgba(79, 172, 254, 0.05);
            transform: translateY(-2px);
        }

        .upload-icon {
            font-size: 4em;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .upload-area.dragover .upload-icon {
            color: #4facfe;
        }

        .upload-text {
            font-size: 1.2em;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .upload-subtext {
            font-size: 0.9em;
            color: #adb5bd;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .file-info {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .file-info.show {
            display: block;
        }

        .file-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .file-size {
            color: #6c757d;
            font-size: 0.9em;
        }

        .options {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .option-group {
            margin-bottom: 15px;
        }

        .option-group:last-child {
            margin-bottom: 0;
        }

        .option-group label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: 500;
            color: #495057;
        }

        .option-group input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .parse-btn {
            width: 100%;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            padding: 18px;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .parse-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(17, 153, 142, 0.3);
        }

        .parse-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            display: none;
        }

        .results.show {
            display: block;
        }

        .results-header {
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .results-title {
            font-size: 1.5em;
            color: #495057;
            margin-bottom: 10px;
        }

        .results-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .meta-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .meta-label {
            font-size: 0.8em;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }

        .meta-value {
            font-size: 1.2em;
            font-weight: 600;
            color: #495057;
        }

        .text-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            max-height: 400px;
            overflow-y: auto;
        }

        .text-line {
            padding: 12px 20px;
            border-bottom: 1px solid #e9ecef;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .text-line:last-child {
            border-bottom: none;
        }

        .text-line:nth-child(even) {
            background: rgba(79, 172, 254, 0.05);
        }

        .line-number {
            display: inline-block;
            width: 50px;
            color: #6c757d;
            font-weight: 600;
            margin-right: 15px;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(102, 126, 234, 0.3);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .upload-section {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .upload-area {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 Document Parser</h1>
            <p>Upload any document and extract its text content instantly</p>
        </div>

        <div class="main-content">
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">Drop your file here</div>
                    <div class="upload-subtext">or click to browse</div>
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Choose File
                    </button>
                    <input type="file" id="fileInput" class="file-input" 
                           accept=".pdf,.docx,.pptx,.xlsx,.txt">
                </div>

                <div class="file-info" id="fileInfo">
                    <div class="file-name" id="fileName"></div>
                    <div class="file-size" id="fileSize"></div>
                </div>

                <div class="options">
                    <div class="option-group">
                        <label>
                            <input type="checkbox" id="useOcr">
                            Enable OCR for image-based PDFs
                        </label>
                    </div>
                    <div class="option-group">
                        <label>
                            <input type="checkbox" id="includeMetadata" checked>
                            Include detailed metadata
                        </label>
                    </div>
                </div>

                <button class="parse-btn" id="parseBtn" onclick="parseDocument()" disabled>
                    🚀 Parse Document
                </button>
            </div>

            <div class="results-section">
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <div>Processing your document...</div>
                    <div style="font-size: 0.9em; color: #6c757d; margin-top: 10px;">
                        This may take a few moments for large files
                    </div>
                </div>

                <div class="results" id="results">
                    <div class="results-header">
                        <div class="results-title" id="resultsTitle">Extracted Text</div>
                        <div class="results-meta" id="resultsMeta"></div>
                    </div>
                    <div class="text-content" id="textContent"></div>
                    <button class="download-btn" onclick="downloadResults()">
                        💾 Download Results as JSON
                    </button>
                </div>

                <div class="error" id="error" style="display: none;">
                    <div id="errorMessage"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'https://parser-gray.vercel.app';
        let selectedFile = null;
        let lastResult = null;

        // File handling
        document.getElementById('fileInput').addEventListener('change', handleFileSelect);
        
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('click', () => document.getElementById('fileInput').click());

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                setSelectedFile(file);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                setSelectedFile(files[0]);
            }
        }

        function setSelectedFile(file) {
            selectedFile = file;
            
            // Show file info
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileInfo').classList.add('show');
            
            // Enable parse button
            document.getElementById('parseBtn').disabled = false;
            
            // Clear previous results
            hideResults();
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        async function parseDocument() {
            if (!selectedFile) {
                showError('No file selected');
                return;
            }

            const parseBtn = document.getElementById('parseBtn');
            
            // Show loading
            showLoading();
            parseBtn.disabled = true;
            parseBtn.textContent = '⏳ Processing...';

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);
                formData.append('use_ocr', document.getElementById('useOcr').checked);
                formData.append('return_metadata', document.getElementById('includeMetadata').checked);

                const response = await fetch(`${API_URL}/parse`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    lastResult = result;
                    showResults(result);
                } else {
                    showError(`Error: ${result.detail || 'Unknown error occurred'}`);
                }

            } catch (error) {
                showError(`Network error: ${error.message}`);
            } finally {
                // Reset button
                parseBtn.disabled = false;
                parseBtn.textContent = '🚀 Parse Document';
                hideLoading();
            }
        }

        function showLoading() {
            document.getElementById('loading').classList.add('show');
            hideResults();
            hideError();
        }

        function hideLoading() {
            document.getElementById('loading').classList.remove('show');
        }

        function showResults(result) {
            hideLoading();
            hideError();

            // Update title
            document.getElementById('resultsTitle').textContent = 
                `Extracted Text from ${result.filename}`;

            // Update metadata
            const metaContainer = document.getElementById('resultsMeta');
            metaContainer.innerHTML = `
                <div class="meta-item">
                    <div class="meta-label">File Type</div>
                    <div class="meta-value">${result.file_type.toUpperCase()}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Text Length</div>
                    <div class="meta-value">${result.total_text_length.toLocaleString()}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Sections</div>
                    <div class="meta-value">${result.text_content.length}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Processed</div>
                    <div class="meta-value">${new Date(result.processed_at).toLocaleTimeString()}</div>
                </div>
            `;

            // Update text content
            const textContainer = document.getElementById('textContent');
            textContainer.innerHTML = '';

            if (result.text_content && result.text_content.length > 0) {
                result.text_content.forEach((item, index) => {
                    const lineDiv = document.createElement('div');
                    lineDiv.className = 'text-line';
                    
                    const lineNumber = item.line || item.page || item.paragraph || (index + 1);
                    lineDiv.innerHTML = `
                        <span class="line-number">${lineNumber}:</span>
                        ${escapeHtml(item.content)}
                    `;
                    
                    textContainer.appendChild(lineDiv);
                });
            } else {
                textContainer.innerHTML = '<div class="text-line">No text content found in the document.</div>';
            }

            document.getElementById('results').classList.add('show');
        }

        function showError(message) {
            hideLoading();
            hideResults();
            
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('error').style.display = 'block';
        }

        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        function hideResults() {
            document.getElementById('results').classList.remove('show');
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function downloadResults() {
            if (!lastResult) return;

            const dataStr = JSON.stringify(lastResult, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `parsed_${lastResult.filename}_${new Date().toISOString().slice(0,10)}.json`;
            link.click();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Document Parser Interface loaded');
            console.log('API URL:', API_URL);
        });
    </script>
</body>
</html>
