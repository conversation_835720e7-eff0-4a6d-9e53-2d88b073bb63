#!/usr/bin/env python3
"""
Advanced Document and PDF Parser - Single Executable File
Supports PDF, DOCX, PPTX, XLSX, and text files with OCR capabilities
Sends parsed data to configured API endpoint

INSTALLATION:
pip install PyPDF2 pdfplumber pymupdf python-docx python-pptx openpyxl xlrd pytesseract Pillow pdf2image requests tqdm chardet

USAGE:
python document_parser.py document.pdf
python document_parser.py document.pdf --ocr --url https://your-api.com/endpoint
python document_parser.py document.pdf --no-send --output results.json

CONFIGURATION:
Set environment variables or modify the CONFIG section below:
- PARSER_API_URL: Your API endpoint
- API_KEY: Optional API key
- API_TOKEN: Optional bearer token
"""

import os
import sys
import json
import logging
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

# Import with fallback error handling
try:
    import requests
except ImportError:
    print("Error: requests not installed. Run: pip install requests")
    sys.exit(1)

try:
    import pdfplumber
except ImportError:
    print("Error: PDF libraries not installed. Run: pip install pdfplumber")
    sys.exit(1)

# Try to import PyMuPDF as fallback (optional)
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    print("Warning: PyMuPDF not available. Using pdfplumber only.")
    PYMUPDF_AVAILABLE = False
    fitz = None

try:
    from docx import Document
except ImportError:
    print("Warning: python-docx not installed. DOCX files won't be supported.")
    Document = None

try:
    from pptx import Presentation
except ImportError:
    print("Warning: python-pptx not installed. PPTX files won't be supported.")
    Presentation = None

try:
    import openpyxl
except ImportError:
    print("Warning: Excel libraries not installed. XLSX files won't be supported.")
    openpyxl = None

try:
    from PIL import Image  # Used for OCR processing
    import pytesseract  # Used dynamically via globals() in OCR methods
    from pdf2image import convert_from_path  # Used dynamically via globals() in OCR methods
    OCR_AVAILABLE = True
    # Reference imports to satisfy static analyzer
    _ = Image, pytesseract, convert_from_path
except ImportError:
    print("Warning: OCR libraries not installed. OCR functionality disabled.")
    OCR_AVAILABLE = False
    # Set globals to None for dynamic access
    pytesseract = None
    convert_from_path = None

try:
    from tqdm import tqdm
except ImportError:
    # Fallback progress bar
    def tqdm(iterable: Any, desc: str = "Processing") -> Any:
        total = len(iterable) if hasattr(iterable, '__len__') else None
        for i, item in enumerate(iterable):
            if total:
                print(f"\r{desc}: {i+1}/{total}", end="", flush=True)
            yield item
        print()

try:
    import chardet
except ImportError:
    print("Warning: chardet not installed. Text encoding detection may fail.")
    chardet = None

try:
    from dotenv import load_dotenv
    # Try to load .env file
    load_dotenv()
    DOTENV_AVAILABLE = True
except ImportError:
    print("Warning: python-dotenv not installed. .env file support disabled.")
    DOTENV_AVAILABLE = False

# CONFIGURATION - Loads from .env file if available, otherwise uses environment variables or defaults
CONFIG = {
    'PARSER_API_URL': os.getenv('PARSER_API_URL', ''),
    'API_KEY': os.getenv('API_KEY', ''),
    'API_TOKEN': os.getenv('API_TOKEN', ''),
    'REQUEST_TIMEOUT': int(os.getenv('REQUEST_TIMEOUT', '30')),
    'MAX_RETRIES': int(os.getenv('MAX_RETRIES', '3')),
    'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO')
}

class DocumentParser:
    """Advanced document parser with multiple extraction methods and API integration"""

    def __init__(self):
        self.api_url = CONFIG['PARSER_API_URL']
        self.api_key = CONFIG['API_KEY']
        self.api_token = CONFIG['API_TOKEN']
        self.request_timeout = CONFIG['REQUEST_TIMEOUT']
        self.max_retries = CONFIG['MAX_RETRIES']

        # Setup logging (Vercel-compatible)
        log_level = CONFIG['LOG_LEVEL']

        # Use only StreamHandler for Vercel (no file logging)
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

        if not self.api_url:
            self.logger.warning("PARSER_API_URL not configured. Set it in CONFIG or environment variables")

        # Check available features
        self.ocr_available = OCR_AVAILABLE
        self.docx_available = Document is not None
        self.pptx_available = Presentation is not None
        self.excel_available = openpyxl is not None
    
    def detect_file_type(self, file_path: str) -> str:
        """Detect file type using MIME type and extension"""
        mime_type, _ = mimetypes.guess_type(file_path)
        extension = Path(file_path).suffix.lower()
        
        if extension == '.pdf' or mime_type == 'application/pdf':
            return 'pdf'
        elif extension in ['.docx', '.doc'] or mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            return 'docx'
        elif extension in ['.pptx', '.ppt'] or mime_type in ['application/vnd.openxmlformats-officedocument.presentationml.presentation']:
            return 'pptx'
        elif extension in ['.xlsx', '.xls'] or mime_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
            return 'xlsx'
        elif extension == '.txt' or mime_type == 'text/plain':
            return 'txt'
        else:
            return 'unknown'
    
    def extract_text_from_pdf(self, file_path: str, use_ocr: bool = False) -> Dict:
        """Extract text from PDF using multiple methods for accuracy"""
        text_content = []
        metadata = {}
        
        try:
            # Method 1: pdfplumber (most accurate for text-based PDFs)
            self.logger.info(f"Extracting text from PDF: {file_path}")
            
            with pdfplumber.open(file_path) as pdf:
                metadata = {
                    'total_pages': len(pdf.pages),
                    'file_size': os.path.getsize(file_path),
                    'extraction_method': 'pdfplumber'
                }
                
                for page_num, page in enumerate(tqdm(pdf.pages, desc="Processing pages"), 1):
                    try:
                        text = page.extract_text()
                        if text and text.strip():
                            text_content.append({
                                'page': page_num,
                                'content': text.strip()
                            })
                        elif use_ocr:
                            # Fallback to OCR for image-based content
                            ocr_text = self._extract_text_with_ocr(file_path, page_num)
                            if ocr_text:
                                text_content.append({
                                    'page': page_num,
                                    'content': ocr_text,
                                    'method': 'ocr'
                                })
                    except Exception as e:
                        self.logger.warning(f"Error processing page {page_num}: {str(e)}")
                        continue
            
            # If no text found, try PyMuPDF as fallback
            if not text_content:
                self.logger.info("No text found with pdfplumber, trying PyMuPDF...")
                text_content = self._extract_with_pymupdf(file_path)
                metadata['extraction_method'] = 'pymupdf'
            
        except Exception as e:
            self.logger.error(f"Error extracting from PDF: {str(e)}")
            return {'error': str(e)}
        
        return {
            'text_content': text_content,
            'metadata': metadata,
            'total_text_length': sum(len(item['content']) for item in text_content)
        }
    
    def _extract_with_pymupdf(self, file_path: str) -> List[Dict]:
        """Fallback extraction using PyMuPDF"""
        text_content = []

        # Check if PyMuPDF is available
        if not PYMUPDF_AVAILABLE or not fitz:
            self.logger.warning("PyMuPDF not available - skipping PyMuPDF extraction")
            return text_content

        try:
            doc = fitz.open(file_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                # Use getattr to safely access the method and handle different PyMuPDF versions
                text = ""
                try:
                    # Try modern PyMuPDF method
                    get_text_method = getattr(page, 'get_text', None)
                    if get_text_method:
                        text = get_text_method()
                    else:
                        # Try older PyMuPDF method
                        get_text_method = getattr(page, 'getText', None)
                        if get_text_method:
                            text = get_text_method()
                        else:
                            # Fallback: try to extract text blocks
                            get_text_blocks_method = getattr(page, 'get_text_blocks', None)
                            if get_text_blocks_method:
                                text_blocks = get_text_blocks_method()
                                text = '\n'.join([block[4] for block in text_blocks if len(block) > 4])
                except Exception as method_error:
                    self.logger.warning(f"Text extraction method failed for page {page_num}: {str(method_error)}")

                if text and text.strip():
                    text_content.append({
                        'page': page_num + 1,
                        'content': text.strip()
                    })
            doc.close()
        except Exception as e:
            self.logger.error(f"PyMuPDF extraction failed: {str(e)}")

        return text_content
    
    def _extract_text_with_ocr(self, file_path: str, page_num: int) -> Optional[str]:
        """Extract text using OCR for image-based PDFs"""
        if not self.ocr_available:
            self.logger.warning("OCR not available - install pytesseract, Pillow, and pdf2image")
            return None

        try:
            # Convert specific page to image
            convert_from_path_func = globals().get('convert_from_path')
            pytesseract_module = globals().get('pytesseract')

            if not convert_from_path_func or not pytesseract_module:
                self.logger.warning("OCR dependencies not available")
                return None

            images = convert_from_path_func(file_path, first_page=page_num, last_page=page_num)
            if images:
                # Use Tesseract OCR
                text = pytesseract_module.image_to_string(images[0])
                return text.strip() if text.strip() else None
        except Exception as e:
            self.logger.warning(f"OCR failed for page {page_num}: {str(e)}")
        return None

    def extract_text_from_docx(self, file_path: str) -> Dict:
        """Extract text from DOCX files"""
        if not self.docx_available:
            return {'error': 'DOCX support not available - install python-docx'}

        try:
            self.logger.info(f"Extracting text from DOCX: {file_path}")

            # Check if Document class is available
            document_class = globals().get('Document')
            if not document_class:
                self.logger.warning("python-docx not available - DOCX extraction skipped")
                return {'error': 'python-docx not installed'}

            doc = document_class(file_path)

            text_content = []
            for i, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():
                    text_content.append({
                        'paragraph': i + 1,
                        'content': paragraph.text.strip()
                    })

            # Extract text from tables
            table_content = []
            for table_num, table in enumerate(doc.tables):
                table_text = []
                for row in table.rows:
                    row_text = [cell.text.strip() for cell in row.cells]
                    table_text.append(row_text)
                table_content.append({
                    'table': table_num + 1,
                    'content': table_text
                })

            metadata = {
                'total_paragraphs': len(text_content),
                'total_tables': len(table_content),
                'file_size': os.path.getsize(file_path)
            }

            return {
                'text_content': text_content,
                'table_content': table_content,
                'metadata': metadata,
                'total_text_length': sum(len(item['content']) for item in text_content)
            }

        except Exception as e:
            self.logger.error(f"Error extracting from DOCX: {str(e)}")
            return {'error': str(e)}

    def extract_text_from_pptx(self, file_path: str) -> Dict:
        """Extract text from PPTX files"""
        if not self.pptx_available:
            return {'error': 'PPTX support not available - install python-pptx'}

        try:
            self.logger.info(f"Extracting text from PPTX: {file_path}")

            # Check if Presentation class is available
            presentation_class = globals().get('Presentation')
            if not presentation_class:
                self.logger.warning("python-pptx not available - PPTX extraction skipped")
                return {'error': 'python-pptx not installed'}

            prs = presentation_class(file_path)

            text_content = []
            for slide_num, slide in enumerate(prs.slides, 1):
                slide_text = []
                for shape in slide.shapes:
                    # Use getattr to safely access text attribute
                    text_attr = getattr(shape, "text", None)
                    if text_attr and text_attr.strip():
                        slide_text.append(text_attr.strip())

                if slide_text:
                    text_content.append({
                        'slide': slide_num,
                        'content': '\n'.join(slide_text)
                    })

            metadata = {
                'total_slides': len(prs.slides),
                'slides_with_text': len(text_content),
                'file_size': os.path.getsize(file_path)
            }

            return {
                'text_content': text_content,
                'metadata': metadata,
                'total_text_length': sum(len(item['content']) for item in text_content)
            }

        except Exception as e:
            self.logger.error(f"Error extracting from PPTX: {str(e)}")
            return {'error': str(e)}

    def extract_text_from_xlsx(self, file_path: str) -> Dict:
        """Extract text from XLSX files"""
        if not self.excel_available:
            return {'error': 'Excel support not available - install openpyxl'}

        try:
            self.logger.info(f"Extracting text from XLSX: {file_path}")

            # Check if openpyxl is available
            openpyxl_module = globals().get('openpyxl')
            if not openpyxl_module:
                self.logger.warning("openpyxl not available - XLSX extraction skipped")
                return {'error': 'openpyxl not installed'}

            workbook = openpyxl_module.load_workbook(file_path, data_only=True)

            text_content = []
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_data = []

                for row in sheet.iter_rows(values_only=True):
                    row_data = [str(cell) if cell is not None else '' for cell in row]
                    if any(cell.strip() for cell in row_data):  # Skip empty rows
                        sheet_data.append(row_data)

                if sheet_data:
                    text_content.append({
                        'sheet': sheet_name,
                        'content': sheet_data
                    })

            metadata = {
                'total_sheets': len(workbook.sheetnames),
                'sheets_with_data': len(text_content),
                'file_size': os.path.getsize(file_path)
            }

            return {
                'text_content': text_content,
                'metadata': metadata,
                'total_cells': sum(len(sheet['content']) for sheet in text_content)
            }

        except Exception as e:
            self.logger.error(f"Error extracting from XLSX: {str(e)}")
            return {'error': str(e)}

    def extract_text_from_txt(self, file_path: str) -> Dict:
        """Extract text from plain text files with encoding detection"""
        try:
            self.logger.info(f"Extracting text from TXT: {file_path}")

            # Detect encoding
            encoding = 'utf-8'
            confidence = 1.0

            if chardet:
                with open(file_path, 'rb') as f:
                    raw_data = f.read()
                    encoding_result = chardet.detect(raw_data)
                    encoding = encoding_result['encoding'] or 'utf-8'
                    confidence = encoding_result['confidence']
            else:
                # Try common encodings if chardet not available
                for enc in ['utf-8', 'latin-1', 'cp1252']:
                    try:
                        with open(file_path, 'r', encoding=enc) as f:
                            f.read()
                        encoding = enc
                        break
                    except UnicodeDecodeError:
                        continue

            # Read file with detected encoding
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()

            # Split into lines for structured output
            lines = content.split('\n')
            text_content = []
            for i, line in enumerate(lines, 1):
                if line.strip():
                    text_content.append({
                        'line': i,
                        'content': line.strip()
                    })

            metadata = {
                'total_lines': len(lines),
                'non_empty_lines': len(text_content),
                'encoding': encoding,
                'confidence': confidence,
                'file_size': os.path.getsize(file_path)
            }

            return {
                'text_content': text_content,
                'metadata': metadata,
                'total_text_length': len(content)
            }

        except Exception as e:
            self.logger.error(f"Error extracting from TXT: {str(e)}")
            return {'error': str(e)}

    def parse_document(self, file_path: str, use_ocr: bool = False) -> Dict:
        """Main method to parse any supported document type"""
        if not os.path.exists(file_path):
            return {'error': f'File not found: {file_path}'}

        file_type = self.detect_file_type(file_path)
        self.logger.info(f"Detected file type: {file_type} for {file_path}")

        # Add common metadata
        common_metadata = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_type': file_type,
            'processed_at': datetime.now().isoformat()
        }

        # Extract based on file type
        if file_type == 'pdf':
            result = self.extract_text_from_pdf(file_path, use_ocr)
        elif file_type == 'docx':
            result = self.extract_text_from_docx(file_path)
        elif file_type == 'pptx':
            result = self.extract_text_from_pptx(file_path)
        elif file_type == 'xlsx':
            result = self.extract_text_from_xlsx(file_path)
        elif file_type == 'txt':
            result = self.extract_text_from_txt(file_path)
        else:
            return {'error': f'Unsupported file type: {file_type}'}

        # Add common metadata to result
        if 'metadata' in result:
            result['metadata'].update(common_metadata)
        else:
            result['metadata'] = common_metadata

        return result

    def send_to_api(self, parsed_data: Dict, endpoint_override: Optional[str] = None) -> Dict:
        """Send parsed document data to configured API endpoint"""
        url = endpoint_override or self.api_url

        if not url:
            return {'error': 'No API URL configured'}

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'DocumentParser/1.0'
        }

        # Add authentication if available
        if self.api_key:
            headers['X-API-Key'] = self.api_key
        if self.api_token:
            headers['Authorization'] = f'Bearer {self.api_token}'

        # Prepare payload
        payload = {
            'timestamp': datetime.now().isoformat(),
            'parser_version': '1.0',
            'data': parsed_data
        }

        # Send with retries
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"Sending data to API (attempt {attempt + 1}/{self.max_retries})")

                response = requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=self.request_timeout
                )

                if response.status_code == 200:
                    self.logger.info("Successfully sent data to API")
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'response': response.json() if response.content else None
                    }
                else:
                    self.logger.warning(f"API returned status {response.status_code}: {response.text}")

            except requests.exceptions.RequestException as e:
                self.logger.error(f"Request failed (attempt {attempt + 1}): {str(e)}")
                if attempt == self.max_retries - 1:
                    return {
                        'error': f'Failed to send data after {self.max_retries} attempts',
                        'last_error': str(e)
                    }

        return {'error': 'Max retries exceeded'}

    def process_and_send(self, file_path: str, use_ocr: bool = False, endpoint_override: Optional[str] = None) -> Dict:
        """Complete workflow: parse document and send to API"""
        self.logger.info(f"Starting complete processing workflow for: {file_path}")

        # Parse document
        parsed_data = self.parse_document(file_path, use_ocr)

        if 'error' in parsed_data:
            self.logger.error(f"Parsing failed: {parsed_data['error']}")
            return parsed_data

        # Send to API
        api_result = self.send_to_api(parsed_data, endpoint_override)

        # Combine results
        return {
            'parsing_result': parsed_data,
            'api_result': api_result,
            'workflow_completed': 'error' not in api_result
        }


def check_installation():
    """Check if all dependencies are properly installed"""
    print("Document Parser - Installation Check")
    print("=" * 50)

    issues = []

    # Check core dependencies
    if not OCR_AVAILABLE:
        issues.append("OCR functionality disabled - install: pip install pytesseract Pillow pdf2image")

    if Document is None:
        issues.append("DOCX support disabled - install: pip install python-docx")

    if Presentation is None:
        issues.append("PPTX support disabled - install: pip install python-pptx")

    if openpyxl is None:
        issues.append("Excel support disabled - install: pip install openpyxl")

    if chardet is None:
        issues.append("Text encoding detection disabled - install: pip install chardet")

    if not DOTENV_AVAILABLE:
        issues.append(".env file support disabled - install: pip install python-dotenv")

    # Test basic functionality
    try:
        parser = DocumentParser()
        print("✓ DocumentParser initialized successfully")
        # Test that parser is working
        if hasattr(parser, 'logger'):
            print("✓ Logger initialized")
    except Exception as e:
        issues.append(f"DocumentParser initialization failed: {str(e)}")

    # Check .env file
    if os.path.exists('.env'):
        print("✓ .env configuration file found")
    else:
        print("⚠ .env file not found - create one to configure your API URL")

    if issues:
        print("\nIssues found:")
        for issue in issues:
            print(f"  ⚠ {issue}")
        print(f"\nInstall missing packages with:")
        print("pip install PyPDF2 pdfplumber pymupdf python-docx python-pptx openpyxl pytesseract Pillow pdf2image requests python-dotenv tqdm chardet")
    else:
        print("✓ All dependencies installed correctly!")

    return len(issues) == 0

def example_usage():
    """Show example usage of the parser"""
    print("\nExample Usage:")
    print("-" * 30)

    # Create a test text file
    test_content = "This is a test document.\nIt has multiple lines.\nFor testing the parser."

    try:
        with open('test_sample.txt', 'w', encoding='utf-8') as f:
            f.write(test_content)

        parser = DocumentParser()
        result = parser.parse_document('test_sample.txt')

        if 'error' not in result:
            print(f"✓ Successfully parsed test file")
            print(f"  - Extracted {result['total_text_length']} characters")
            print(f"  - Found {len(result['text_content'])} non-empty lines")
            print(f"  - Detected encoding: {result['metadata']['encoding']}")
        else:
            print(f"✗ Test failed: {result['error']}")

        # Clean up
        os.remove('test_sample.txt')

    except Exception as e:
        print(f"✗ Example failed: {str(e)}")

def main():
    """Command line interface for the document parser"""
    import argparse

    parser = argparse.ArgumentParser(
        description='Advanced Document and PDF Parser - Single Executable',
        epilog="""
Examples:
  python document_parser.py document.pdf
  python document_parser.py document.pdf --ocr
  python document_parser.py document.pdf --no-send --output results.json
  python document_parser.py document.pdf --url https://your-api.com/endpoint
  python document_parser.py --check-install
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument('file_path', nargs='?', help='Path to the document to parse')
    parser.add_argument('--ocr', action='store_true', help='Enable OCR for image-based PDFs')
    parser.add_argument('--no-send', action='store_true', help='Parse only, do not send to API')
    parser.add_argument('--url', help='Override API URL from configuration')
    parser.add_argument('--output', help='Save parsed data to JSON file')
    parser.add_argument('--check-install', action='store_true', help='Check installation and dependencies')
    parser.add_argument('--example', action='store_true', help='Run example usage')

    args = parser.parse_args()

    # Handle special commands
    if args.check_install:
        success = check_installation()
        if success:
            example_usage()
        return

    if args.example:
        example_usage()
        return

    if not args.file_path:
        parser.print_help()
        return

    # Check if file exists
    if not os.path.exists(args.file_path):
        print(f"Error: File not found: {args.file_path}")
        return

    # Initialize parser
    try:
        doc_parser = DocumentParser()
    except Exception as e:
        print(f"Error initializing parser: {str(e)}")
        print("Run with --check-install to verify dependencies")
        return

    print(f"Processing: {args.file_path}")
    print(f"File type: {doc_parser.detect_file_type(args.file_path)}")

    if args.no_send:
        # Parse only
        result = doc_parser.parse_document(args.file_path, args.ocr)

        if 'error' not in result:
            print("✓ Parsing completed successfully")
            print(f"  - Extracted {result.get('total_text_length', 0)} characters")
            if 'metadata' in result:
                if 'total_pages' in result['metadata']:
                    print(f"  - Processed {result['metadata']['total_pages']} pages")
                print(f"  - File size: {result['metadata']['file_size']} bytes")
        else:
            print(f"✗ Parsing failed: {result['error']}")

        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"Results saved to: {args.output}")
        elif 'error' not in result:
            print("\nFirst few lines of extracted text:")
            text_content = result.get('text_content', [])
            for i, item in enumerate(text_content[:3]):
                content = item.get('content', '')[:100]
                print(f"  {i+1}: {content}{'...' if len(content) == 100 else ''}")
    else:
        # Complete workflow
        if not doc_parser.api_url:
            print("Warning: No API URL configured. Set PARSER_API_URL in CONFIG or use --url")
            print("Proceeding with parse-only mode...")
            result = doc_parser.parse_document(args.file_path, args.ocr)

            if 'error' not in result:
                print("✓ Parsing completed successfully")
            else:
                print(f"✗ Parsing failed: {result['error']}")
        else:
            result = doc_parser.process_and_send(args.file_path, args.ocr, args.url)

            if result['workflow_completed']:
                print("✓ Document parsed and sent to API successfully")
                if 'api_result' in result and 'status_code' in result['api_result']:
                    print(f"  - API Status: {result['api_result']['status_code']}")
            else:
                print("✗ Workflow failed")
                if 'parsing_result' in result and 'error' in result['parsing_result']:
                    print(f"  - Parsing error: {result['parsing_result']['error']}")
                if 'api_result' in result and 'error' in result['api_result']:
                    print(f"  - API error: {result['api_result']['error']}")

        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"Results saved to: {args.output}")


if __name__ == "__main__":
    main()
