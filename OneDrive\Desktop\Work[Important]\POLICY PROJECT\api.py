#!/usr/bin/env python3
"""
FastAPI Web API for Document Parser
Designed for Vercel deployment
"""

import os
import io
import json
import logging
from typing import Dict, List, Optional
from datetime import datetime
import tempfile

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import the document parser class
from document_parser import DocumentParser

# Initialize FastAPI app
app = FastAPI(
    title="Document Parser API",
    description="Parse PDF, DOCX, PPTX, XLSX files and extract text content",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize document parser
parser = DocumentParser()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Document Parser API is running",
        "version": "1.0.0",
        "supported_formats": ["PDF", "DOCX", "PPTX", "XLSX", "TXT"],
        "features": {
            "ocr_available": parser.ocr_available,
            "docx_available": parser.docx_available,
            "pptx_available": parser.pptx_available,
            "excel_available": parser.excel_available
        }
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "dependencies": {
            "ocr": parser.ocr_available,
            "docx": parser.docx_available,
            "pptx": parser.pptx_available,
            "excel": parser.excel_available
        }
    }

@app.post("/parse")
async def parse_document(
    file: UploadFile = File(...),
    use_ocr: bool = Form(False),
    return_metadata: bool = Form(True)
):
    """
    Parse an uploaded document and return extracted text
    
    - **file**: The document file to parse (PDF, DOCX, PPTX, XLSX, TXT)
    - **use_ocr**: Whether to use OCR for image-based PDFs
    - **return_metadata**: Whether to include metadata in response
    """
    
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Check file size (limit to 10MB for Vercel)
    max_size = 10 * 1024 * 1024  # 10MB
    file_content = await file.read()
    if len(file_content) > max_size:
        raise HTTPException(status_code=413, detail="File too large. Maximum size is 10MB")
    
    try:
        # Create temporary file (Vercel allows /tmp directory)
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name
        
        # Parse the document
        result = parser.parse_document(temp_file_path, use_ocr)
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        # Handle parsing errors
        if 'error' in result:
            raise HTTPException(status_code=422, detail=f"Parsing failed: {result['error']}")
        
        # Prepare response
        response_data = {
            "success": True,
            "filename": file.filename,
            "file_type": result.get('metadata', {}).get('file_type', 'unknown'),
            "processed_at": datetime.now().isoformat(),
            "text_content": result.get('text_content', []),
            "total_text_length": result.get('total_text_length', 0)
        }
        
        # Add metadata if requested
        if return_metadata:
            response_data["metadata"] = result.get('metadata', {})
            if 'table_content' in result:
                response_data["table_content"] = result['table_content']
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        # Clean up temp file if it exists
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/parse-url")
async def parse_document_from_url(
    url: str = Form(...),
    use_ocr: bool = Form(False),
    return_metadata: bool = Form(True)
):
    """
    Parse a document from a URL
    
    - **url**: URL of the document to parse
    - **use_ocr**: Whether to use OCR for image-based PDFs
    - **return_metadata**: Whether to include metadata in response
    """
    
    try:
        import requests
        
        # Download file from URL
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Check file size
        max_size = 10 * 1024 * 1024  # 10MB
        if len(response.content) > max_size:
            raise HTTPException(status_code=413, detail="File too large. Maximum size is 10MB")
        
        # Get filename from URL or use default
        filename = url.split('/')[-1] or 'document'
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
            temp_file.write(response.content)
            temp_file_path = temp_file.name
        
        # Parse the document
        result = parser.parse_document(temp_file_path, use_ocr)
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        # Handle parsing errors
        if 'error' in result:
            raise HTTPException(status_code=422, detail=f"Parsing failed: {result['error']}")
        
        # Prepare response
        response_data = {
            "success": True,
            "source_url": url,
            "filename": filename,
            "file_type": result.get('metadata', {}).get('file_type', 'unknown'),
            "processed_at": datetime.now().isoformat(),
            "text_content": result.get('text_content', []),
            "total_text_length": result.get('total_text_length', 0)
        }
        
        # Add metadata if requested
        if return_metadata:
            response_data["metadata"] = result.get('metadata', {})
            if 'table_content' in result:
                response_data["table_content"] = result['table_content']
        
        return JSONResponse(content=response_data)
        
    except requests.RequestException as e:
        raise HTTPException(status_code=400, detail=f"Failed to download file: {str(e)}")
    except Exception as e:
        # Clean up temp file if it exists
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/supported-formats")
async def get_supported_formats():
    """Get list of supported file formats"""
    return {
        "formats": {
            "PDF": {
                "extensions": [".pdf"],
                "description": "Portable Document Format",
                "ocr_supported": parser.ocr_available
            },
            "DOCX": {
                "extensions": [".docx"],
                "description": "Microsoft Word Document",
                "available": parser.docx_available
            },
            "PPTX": {
                "extensions": [".pptx"],
                "description": "Microsoft PowerPoint Presentation",
                "available": parser.pptx_available
            },
            "XLSX": {
                "extensions": [".xlsx"],
                "description": "Microsoft Excel Spreadsheet",
                "available": parser.excel_available
            },
            "TXT": {
                "extensions": [".txt"],
                "description": "Plain Text File",
                "available": True
            }
        }
    }

# For local development
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8081)
